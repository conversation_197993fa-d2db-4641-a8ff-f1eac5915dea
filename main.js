/**
 * Counter Strike Manager Simulator
 * Phaser 3 Version: 3.70.0
 * A strategic team management game where players build and manage a CS esports team
 */

// Game Configuration
const gameConfig = {
    CANVAS_WIDTH: 1024,
    CANVAS_HEIGHT: 768,
    STARTING_BUDGET: 10000,
    STARTING_TEAM_SIZE: 3,
    MAX_TEAM_SIZE: 5,
    WIN_CONDITION: 10,
    LOSE_BUDGET: -5000,
    COLORS: {
        PRIMARY: 0x1a1a2e,
        SECONDARY: 0xff6b35,
        ACCENT: 0x00d4ff,
        TEXT: 0xe5e5e5,
        WARNING: 0xff4757,
        SUCCESS: 0x2ed573
    }
};

// Game State Management
class GameState {
    constructor() {
        this.budget = gameConfig.STARTING_BUDGET;
        this.team = [];
        this.victories = 0;
        this.round = 1;
        this.difficulty = 1;
        
        // Initialize starting team
        for (let i = 0; i < gameConfig.STARTING_TEAM_SIZE; i++) {
            this.team.push(this.generatePlayer(`Player ${i + 1}`, 30 + Math.random() * 20));
        }
    }
    
    generatePlayer(name, skill = null) {
        return {
            name: name,
            skill: skill || (20 + Math.random() * 30),
            cost: Math.floor((skill || 25) * 100 + Math.random() * 1000)
        };
    }
    
    getTeamSkill() {
        return this.team.reduce((total, player) => total + player.skill, 0) / this.team.length;
    }
    
    canAfford(cost) {
        return this.budget >= cost;
    }
    
    isGameWon() {
        return this.victories >= gameConfig.WIN_CONDITION && this.budget > 0;
    }
    
    isGameLost() {
        return this.budget <= gameConfig.LOSE_BUDGET;
    }
}

// Main Game Scene
class GameScene extends Phaser.Scene {
    constructor() {
        super({ key: 'GameScene' });
        this.gameState = new GameState();
        this.particles = null;
    }
    
    preload() {
        // Create simple colored rectangles as placeholders for sprites
        this.load.image('button', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');

        // Skip audio loading to ensure game works offline
        console.log('Preload complete - audio disabled for offline compatibility');
    }
    
    create() {
        // Background
        this.add.rectangle(512, 384, 1024, 768, gameConfig.COLORS.PRIMARY);

        // Simplified particle system - create only when needed
        this.particles = null;
        
        // UI Setup
        this.createUI();
        this.updateDisplay();
        
        // Audio setup (with error handling) - Skip for now to ensure game works
        this.audioEnabled = false;
        console.log('Audio disabled for offline compatibility');
    }
    
    createUI() {
        // Title
        this.add.text(512, 50, 'CS MANAGER SIMULATOR', {
            fontSize: '32px',
            fill: '#ff6b35',
            fontFamily: 'Courier New',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // Status Panel
        this.statusText = this.add.text(50, 100, '', {
            fontSize: '16px',
            fill: '#e5e5e5',
            fontFamily: 'Courier New',
            lineSpacing: 5
        });

        // Team Panel
        this.teamText = this.add.text(50, 250, '', {
            fontSize: '14px',
            fill: '#e5e5e5',
            fontFamily: 'Courier New',
            lineSpacing: 3
        });

        // Action Buttons
        this.createButton(600, 150, 'RECRUIT PLAYER', () => this.recruitPlayer());
        this.createButton(600, 200, 'TRAIN TEAM', () => this.trainTeam());
        this.createButton(600, 250, 'COMPETE MATCH', () => this.competeMatch());
        this.createButton(600, 350, 'NEW GAME', () => this.newGame());

        // Available Players Panel
        this.availableText = this.add.text(600, 400, '', {
            fontSize: '12px',
            fill: '#00d4ff',
            fontFamily: 'Courier New',
            lineSpacing: 2
        });

        this.generateAvailablePlayers();
    }

    createButton(x, y, text, callback) {
        const button = this.add.rectangle(x, y, 180, 35, gameConfig.COLORS.SECONDARY)
            .setInteractive()
            .on('pointerdown', () => {
                this.playSound('clickSound');
                this.createButtonEffect(x, y);
                callback();
            })
            .on('pointerover', () => {
                button.setFillStyle(gameConfig.COLORS.ACCENT);
            })
            .on('pointerout', () => {
                button.setFillStyle(gameConfig.COLORS.SECONDARY);
            });

        this.add.text(x, y, text, {
            fontSize: '12px',
            fill: '#ffffff',
            fontFamily: 'Courier New',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        return button;
    }

    createButtonEffect(x, y) {
        // Simple visual feedback without particles
        const effect = this.add.circle(x, y, 20, gameConfig.COLORS.ACCENT, 0.5);
        this.tweens.add({
            targets: effect,
            scaleX: 2,
            scaleY: 2,
            alpha: 0,
            duration: 300,
            onComplete: () => effect.destroy()
        });
    }

    generateAvailablePlayers() {
        this.availablePlayers = [];
        for (let i = 0; i < 3; i++) {
            const skill = 25 + Math.random() * 40 + (this.gameState.round * 2);
            this.availablePlayers.push(
                this.gameState.generatePlayer(`Recruit ${i + 1}`, skill)
            );
        }
    }

    recruitPlayer() {
        if (this.gameState.team.length >= gameConfig.MAX_TEAM_SIZE) {
            this.showMessage('Team is full! (Max 5 players)', gameConfig.COLORS.WARNING);
            return;
        }

        if (this.availablePlayers.length === 0) {
            this.showMessage('No players available!', gameConfig.COLORS.WARNING);
            return;
        }

        const player = this.availablePlayers[0];
        if (!this.gameState.canAfford(player.cost)) {
            this.showMessage('Not enough budget!', gameConfig.COLORS.WARNING);
            this.playSound('failSound');
            return;
        }

        this.gameState.budget -= player.cost;
        this.gameState.team.push(player);
        this.availablePlayers.shift();

        this.playSound('successSound');
        this.showMessage(`Recruited ${player.name}!`, gameConfig.COLORS.SUCCESS);
        this.updateDisplay();
    }

    trainTeam() {
        const cost = 1000 + (this.gameState.round * 200);
        if (!this.gameState.canAfford(cost)) {
            this.showMessage('Not enough budget for training!', gameConfig.COLORS.WARNING);
            this.playSound('failSound');
            return;
        }

        this.gameState.budget -= cost;
        this.gameState.team.forEach(player => {
            player.skill += 3 + Math.random() * 4;
        });

        this.playSound('successSound');
        this.showMessage('Team training completed!', gameConfig.COLORS.SUCCESS);
        this.updateDisplay();
    }

    competeMatch() {
        const teamSkill = this.gameState.getTeamSkill();
        const opponentSkill = 40 + (this.gameState.difficulty * 8) + Math.random() * 20;

        // Match simulation with some randomness
        const skillDifference = teamSkill - opponentSkill;
        const winChance = 0.5 + (skillDifference / 100);
        const matchResult = Math.random() < Math.max(0.1, Math.min(0.9, winChance));

        if (matchResult) {
            // Victory
            const prize = 2000 + (this.gameState.round * 300);
            this.gameState.budget += prize;
            this.gameState.victories++;
            this.gameState.difficulty += 0.2;

            this.playSound('successSound');
            this.showMessage(`VICTORY! +$${prize}`, gameConfig.COLORS.SUCCESS);
            this.createVictoryEffect();
        } else {
            // Defeat
            const penalty = 500 + (this.gameState.round * 100);
            this.gameState.budget -= penalty;

            this.playSound('failSound');
            this.showMessage(`DEFEAT! -$${penalty}`, gameConfig.COLORS.WARNING);
        }

        this.gameState.round++;
        this.generateAvailablePlayers();
        this.updateDisplay();
        this.checkGameEnd();
    }

    createVictoryEffect() {
        // Create celebration effect without particles
        for (let i = 0; i < 5; i++) {
            this.time.delayedCall(i * 100, () => {
                const star = this.add.text(
                    400 + Math.random() * 200,
                    200 + Math.random() * 100,
                    '⭐',
                    { fontSize: '24px' }
                );
                this.tweens.add({
                    targets: star,
                    y: star.y - 50,
                    alpha: 0,
                    duration: 1000,
                    onComplete: () => star.destroy()
                });
            });
        }
    }

    newGame() {
        this.gameState = new GameState();
        this.generateAvailablePlayers();
        this.updateDisplay();
        this.showMessage('New game started!', gameConfig.COLORS.ACCENT);
    }

    updateDisplay() {
        // Update status
        this.statusText.setText([
            `💰 Budget: $${this.gameState.budget.toLocaleString()}`,
            `🏆 Victories: ${this.gameState.victories}/${gameConfig.WIN_CONDITION}`,
            `📈 Round: ${this.gameState.round}`,
            `⚡ Team Skill: ${this.gameState.getTeamSkill().toFixed(1)}`
        ]);

        // Update team display
        let teamDisplay = '👥 YOUR TEAM:\n';
        this.gameState.team.forEach((player, index) => {
            teamDisplay += `${index + 1}. ${player.name} (Skill: ${player.skill.toFixed(1)})\n`;
        });
        this.teamText.setText(teamDisplay);

        // Update available players
        let availableDisplay = '🔍 AVAILABLE RECRUITS:\n';
        this.availablePlayers.forEach((player, index) => {
            availableDisplay += `${index + 1}. ${player.name}\n`;
            availableDisplay += `   Skill: ${player.skill.toFixed(1)} | Cost: $${player.cost}\n`;
        });
        this.availableText.setText(availableDisplay);
    }

    showMessage(text, color) {
        if (this.messageText) {
            this.messageText.destroy();
        }

        this.messageText = this.add.text(512, 600, text, {
            fontSize: '18px',
            fill: `#${color.toString(16).padStart(6, '0')}`,
            fontFamily: 'Courier New',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // Fade out message after 2 seconds
        this.tweens.add({
            targets: this.messageText,
            alpha: 0,
            duration: 2000,
            onComplete: () => {
                if (this.messageText) {
                    this.messageText.destroy();
                    this.messageText = null;
                }
            }
        });
    }

    playSound(soundKey) {
        // Audio disabled for offline compatibility
        // In a production version, you would load actual audio files here
        if (this.audioEnabled && this[soundKey]) {
            try {
                this[soundKey].play();
            } catch (error) {
                console.log('Audio playback failed:', error);
            }
        }
    }

    checkGameEnd() {
        if (this.gameState.isGameWon()) {
            this.showMessage('🎉 CONGRATULATIONS! YOU WON! 🎉', gameConfig.COLORS.SUCCESS);
            this.createVictoryEffect();
        } else if (this.gameState.isGameLost()) {
            this.showMessage('💀 GAME OVER - BUDGET DEPLETED! 💀', gameConfig.COLORS.WARNING);
        }
    }
}

// Menu Scene
class MenuScene extends Phaser.Scene {
    constructor() {
        super({ key: 'MenuScene' });
        console.log('MenuScene constructor called');
    }

    create() {
        console.log('MenuScene create() called');
        console.log('Canvas size:', this.sys.game.canvas.width, 'x', this.sys.game.canvas.height);

        // Background
        this.add.rectangle(512, 384, 1024, 768, gameConfig.COLORS.PRIMARY);

        // Title with glow effect
        const title = this.add.text(512, 200, 'COUNTER STRIKE\nMANAGER SIMULATOR', {
            fontSize: '48px',
            fill: '#ff6b35',
            fontFamily: 'Courier New',
            fontWeight: 'bold',
            align: 'center',
            lineSpacing: 10
        }).setOrigin(0.5);

        // Animated title glow
        this.tweens.add({
            targets: title,
            scaleX: 1.05,
            scaleY: 1.05,
            duration: 2000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });

        // Subtitle
        this.add.text(512, 320, 'Build the ultimate esports team!', {
            fontSize: '24px',
            fill: '#00d4ff',
            fontFamily: 'Courier New'
        }).setOrigin(0.5);

        // Start button
        const startButton = this.add.rectangle(512, 450, 200, 60, gameConfig.COLORS.SECONDARY)
            .setInteractive()
            .on('pointerdown', () => {
                this.scene.start('GameScene');
            })
            .on('pointerover', () => {
                startButton.setFillStyle(gameConfig.COLORS.ACCENT);
            })
            .on('pointerout', () => {
                startButton.setFillStyle(gameConfig.COLORS.SECONDARY);
            });

        this.add.text(512, 450, 'START GAME', {
            fontSize: '20px',
            fill: '#ffffff',
            fontFamily: 'Courier New',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // Instructions
        this.add.text(512, 550, 'Manage your budget • Recruit players • Train your team • Win matches', {
            fontSize: '16px',
            fill: '#e5e5e5',
            fontFamily: 'Courier New',
            alpha: 0.8
        }).setOrigin(0.5);

        // Animated background elements
        this.createBackgroundAnimation();
    }

    createBackgroundAnimation() {
        // Simplified background animation
        for (let i = 0; i < 10; i++) {
            const dot = this.add.circle(
                Math.random() * 1024,
                Math.random() * 768,
                2,
                gameConfig.COLORS.ACCENT,
                0.2
            );

            this.tweens.add({
                targets: dot,
                alpha: 0.5,
                duration: 2000,
                yoyo: true,
                repeat: -1,
                delay: Math.random() * 1000
            });
        }
    }
}

// Game Configuration and Initialization
const config = {
    type: Phaser.AUTO,
    width: 1024,
    height: 768,
    parent: 'gameContainer',
    backgroundColor: '#1a1a2e',
    scene: [MenuScene, GameScene]
};

// Initialize the game
const game = new Phaser.Game(config);
