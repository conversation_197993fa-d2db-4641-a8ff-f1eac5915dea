<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Counter Strike Manager Simulator - Fixed</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            font-family: 'Courier New', monospace;
            color: #e5e5e5;
            overflow: hidden;
        }
        
        #gameContainer {
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }
        
        #instructions {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(26, 26, 46, 0.9);
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #ff6b35;
            max-width: 300px;
            z-index: 1000;
        }
        
        #instructions h3 {
            color: #ff6b35;
            margin: 0 0 10px 0;
            font-size: 16px;
        }
        
        #instructions p {
            margin: 5px 0;
            font-size: 12px;
            line-height: 1.4;
        }
        
        #instructions .controls {
            color: #00d4ff;
            font-weight: bold;
        }
        
        canvas {
            border: 2px solid #ff6b35;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div id="instructions">
            <h3>🎯 CS Manager Simulator</h3>
            <p><span class="controls">GOAL:</span> Build the ultimate CS team! Recruit players, train them, and win 10 matches while managing your budget.</p>
            <p><span class="controls">CONTROLS:</span> Click buttons to recruit, train, and compete. Balance your resources wisely!</p>
            <p><span class="controls">WIN:</span> 10 victories with positive budget</p>
            <p><span class="controls">LOSE:</span> Budget below -$5000</p>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script>
        console.log('Starting game initialization...');
        console.log('Phaser version:', Phaser.VERSION);
        
        // Game Configuration
        const gameConfig = {
            CANVAS_WIDTH: 1024,
            CANVAS_HEIGHT: 768,
            STARTING_BUDGET: 10000,
            STARTING_TEAM_SIZE: 3,
            MAX_TEAM_SIZE: 5,
            WIN_CONDITION: 10,
            LOSE_BUDGET: -5000,
            COLORS: {
                PRIMARY: 0x1a1a2e,
                SECONDARY: 0xff6b35,
                ACCENT: 0x00d4ff,
                TEXT: 0xe5e5e5,
                WARNING: 0xff4757,
                SUCCESS: 0x2ed573
            }
        };

        // Game State Management
        class GameState {
            constructor() {
                this.budget = gameConfig.STARTING_BUDGET;
                this.team = [];
                this.victories = 0;
                this.round = 1;
                this.difficulty = 1;
                
                // Initialize starting team
                for (let i = 0; i < gameConfig.STARTING_TEAM_SIZE; i++) {
                    this.team.push(this.generatePlayer(`Player ${i + 1}`, 30 + Math.random() * 20));
                }
            }
            
            generatePlayer(name, skill = null) {
                return {
                    name: name,
                    skill: skill || (20 + Math.random() * 30),
                    cost: Math.floor((skill || 25) * 100 + Math.random() * 1000)
                };
            }
            
            getTeamSkill() {
                return this.team.reduce((total, player) => total + player.skill, 0) / this.team.length;
            }
            
            canAfford(cost) {
                return this.budget >= cost;
            }
            
            isGameWon() {
                return this.victories >= gameConfig.WIN_CONDITION && this.budget > 0;
            }
            
            isGameLost() {
                return this.budget <= gameConfig.LOSE_BUDGET;
            }
        }

        // Menu Scene
        class MenuScene extends Phaser.Scene {
            constructor() {
                super({ key: 'MenuScene' });
                console.log('MenuScene constructor');
            }
            
            create() {
                console.log('MenuScene create() called');
                
                // Background
                this.add.rectangle(512, 384, 1024, 768, gameConfig.COLORS.PRIMARY);
                
                // Title
                const title = this.add.text(512, 200, 'COUNTER STRIKE\nMANAGER SIMULATOR', {
                    fontSize: '48px',
                    fill: '#ff6b35',
                    fontFamily: 'Courier New',
                    fontWeight: 'bold',
                    align: 'center',
                    lineSpacing: 10
                }).setOrigin(0.5);
                
                // Start button
                const startButton = this.add.rectangle(512, 450, 200, 60, gameConfig.COLORS.SECONDARY)
                    .setInteractive()
                    .on('pointerdown', () => {
                        console.log('Start button clicked');
                        this.scene.start('GameScene');
                    })
                    .on('pointerover', () => {
                        startButton.setFillStyle(gameConfig.COLORS.ACCENT);
                    })
                    .on('pointerout', () => {
                        startButton.setFillStyle(gameConfig.COLORS.SECONDARY);
                    });
                    
                this.add.text(512, 450, 'START GAME', {
                    fontSize: '20px',
                    fill: '#ffffff',
                    fontFamily: 'Courier New',
                    fontWeight: 'bold'
                }).setOrigin(0.5);
                
                console.log('MenuScene setup complete');
            }
        }

        // Simple Game Scene
        class GameScene extends Phaser.Scene {
            constructor() {
                super({ key: 'GameScene' });
                console.log('GameScene constructor');
                this.gameState = new GameState();
                this.availablePlayers = [];
            }
            
            create() {
                console.log('GameScene create() called');
                
                // Background
                this.add.rectangle(512, 384, 1024, 768, gameConfig.COLORS.PRIMARY);
                
                // Title
                this.add.text(512, 50, 'CS MANAGER SIMULATOR', {
                    fontSize: '32px',
                    fill: '#ff6b35',
                    fontFamily: 'Courier New',
                    fontWeight: 'bold'
                }).setOrigin(0.5);
                
                // Status Panel
                this.statusText = this.add.text(50, 100, '', {
                    fontSize: '16px',
                    fill: '#e5e5e5',
                    fontFamily: 'Courier New',
                    lineSpacing: 5
                });
                
                // Team Panel
                this.teamText = this.add.text(50, 250, '', {
                    fontSize: '14px',
                    fill: '#e5e5e5',
                    fontFamily: 'Courier New',
                    lineSpacing: 3
                });
                
                // Action Buttons
                this.createButton(600, 150, 'RECRUIT PLAYER', () => this.recruitPlayer());
                this.createButton(600, 200, 'TRAIN TEAM', () => this.trainTeam());
                this.createButton(600, 250, 'COMPETE MATCH', () => this.competeMatch());
                this.createButton(600, 350, 'BACK TO MENU', () => this.scene.start('MenuScene'));
                
                // Available Players Panel
                this.availableText = this.add.text(600, 400, '', {
                    fontSize: '12px',
                    fill: '#00d4ff',
                    fontFamily: 'Courier New',
                    lineSpacing: 2
                });
                
                this.generateAvailablePlayers();
                this.updateDisplay();
                
                console.log('GameScene setup complete');
            }
            
            createButton(x, y, text, callback) {
                const button = this.add.rectangle(x, y, 180, 35, gameConfig.COLORS.SECONDARY)
                    .setInteractive()
                    .on('pointerdown', callback)
                    .on('pointerover', () => {
                        button.setFillStyle(gameConfig.COLORS.ACCENT);
                    })
                    .on('pointerout', () => {
                        button.setFillStyle(gameConfig.COLORS.SECONDARY);
                    });
                    
                this.add.text(x, y, text, {
                    fontSize: '12px',
                    fill: '#ffffff',
                    fontFamily: 'Courier New',
                    fontWeight: 'bold'
                }).setOrigin(0.5);
                
                return button;
            }
            
            generateAvailablePlayers() {
                this.availablePlayers = [];
                for (let i = 0; i < 3; i++) {
                    const skill = 25 + Math.random() * 40 + (this.gameState.round * 2);
                    this.availablePlayers.push(
                        this.gameState.generatePlayer(`Recruit ${i + 1}`, skill)
                    );
                }
            }
            
            recruitPlayer() {
                if (this.gameState.team.length >= gameConfig.MAX_TEAM_SIZE) {
                    this.showMessage('Team is full! (Max 5 players)', gameConfig.COLORS.WARNING);
                    return;
                }
                
                if (this.availablePlayers.length === 0) {
                    this.showMessage('No players available!', gameConfig.COLORS.WARNING);
                    return;
                }
                
                const player = this.availablePlayers[0];
                if (!this.gameState.canAfford(player.cost)) {
                    this.showMessage('Not enough budget!', gameConfig.COLORS.WARNING);
                    return;
                }
                
                this.gameState.budget -= player.cost;
                this.gameState.team.push(player);
                this.availablePlayers.shift();
                
                this.showMessage(`Recruited ${player.name}!`, gameConfig.COLORS.SUCCESS);
                this.updateDisplay();
            }
            
            trainTeam() {
                const cost = 1000 + (this.gameState.round * 200);
                if (!this.gameState.canAfford(cost)) {
                    this.showMessage('Not enough budget for training!', gameConfig.COLORS.WARNING);
                    return;
                }
                
                this.gameState.budget -= cost;
                this.gameState.team.forEach(player => {
                    player.skill += 3 + Math.random() * 4;
                });
                
                this.showMessage('Team training completed!', gameConfig.COLORS.SUCCESS);
                this.updateDisplay();
            }
            
            competeMatch() {
                const teamSkill = this.gameState.getTeamSkill();
                const opponentSkill = 40 + (this.gameState.difficulty * 8) + Math.random() * 20;
                
                const skillDifference = teamSkill - opponentSkill;
                const winChance = 0.5 + (skillDifference / 100);
                const matchResult = Math.random() < Math.max(0.1, Math.min(0.9, winChance));
                
                if (matchResult) {
                    const prize = 2000 + (this.gameState.round * 300);
                    this.gameState.budget += prize;
                    this.gameState.victories++;
                    this.gameState.difficulty += 0.2;
                    
                    this.showMessage(`VICTORY! +$${prize}`, gameConfig.COLORS.SUCCESS);
                } else {
                    const penalty = 500 + (this.gameState.round * 100);
                    this.gameState.budget -= penalty;
                    
                    this.showMessage(`DEFEAT! -$${penalty}`, gameConfig.COLORS.WARNING);
                }
                
                this.gameState.round++;
                this.generateAvailablePlayers();
                this.updateDisplay();
                this.checkGameEnd();
            }
            
            updateDisplay() {
                this.statusText.setText([
                    `💰 Budget: $${this.gameState.budget.toLocaleString()}`,
                    `🏆 Victories: ${this.gameState.victories}/${gameConfig.WIN_CONDITION}`,
                    `📈 Round: ${this.gameState.round}`,
                    `⚡ Team Skill: ${this.gameState.getTeamSkill().toFixed(1)}`
                ]);
                
                let teamDisplay = '👥 YOUR TEAM:\n';
                this.gameState.team.forEach((player, index) => {
                    teamDisplay += `${index + 1}. ${player.name} (Skill: ${player.skill.toFixed(1)})\n`;
                });
                this.teamText.setText(teamDisplay);
                
                let availableDisplay = '🔍 AVAILABLE RECRUITS:\n';
                this.availablePlayers.forEach((player, index) => {
                    availableDisplay += `${index + 1}. ${player.name}\n`;
                    availableDisplay += `   Skill: ${player.skill.toFixed(1)} | Cost: $${player.cost}\n`;
                });
                this.availableText.setText(availableDisplay);
            }
            
            showMessage(text, color) {
                if (this.messageText) {
                    this.messageText.destroy();
                }
                
                this.messageText = this.add.text(512, 600, text, {
                    fontSize: '18px',
                    fill: `#${color.toString(16).padStart(6, '0')}`,
                    fontFamily: 'Courier New',
                    fontWeight: 'bold'
                }).setOrigin(0.5);
                
                this.time.delayedCall(2000, () => {
                    if (this.messageText) {
                        this.messageText.destroy();
                        this.messageText = null;
                    }
                });
            }
            
            checkGameEnd() {
                if (this.gameState.isGameWon()) {
                    this.showMessage('🎉 CONGRATULATIONS! YOU WON! 🎉', gameConfig.COLORS.SUCCESS);
                } else if (this.gameState.isGameLost()) {
                    this.showMessage('💀 GAME OVER - BUDGET DEPLETED! 💀', gameConfig.COLORS.WARNING);
                }
            }
        }

        // Game Configuration
        const config = {
            type: Phaser.AUTO,
            width: 1024,
            height: 768,
            parent: 'gameContainer',
            backgroundColor: '#1a1a2e',
            scene: [MenuScene, GameScene]
        };

        // Initialize the game
        console.log('Creating Phaser game...');
        const game = new Phaser.Game(config);
        console.log('Game created successfully');
    </script>
</body>
</html>
