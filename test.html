<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phaser Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a2e;
            color: white;
            font-family: Arial, sans-serif;
        }
        #gameContainer {
            width: 800px;
            height: 600px;
            margin: 20px auto;
            border: 2px solid #ff6b35;
        }
    </style>
</head>
<body>
    <h1>Phaser 3 Test</h1>
    <div id="gameContainer"></div>
    <div id="debug"></div>
    
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script>
        console.log('Phaser version:', Phaser.VERSION);
        document.getElementById('debug').innerHTML = 'Phaser version: ' + Phaser.VERSION;
        
        class TestScene extends Phaser.Scene {
            constructor() {
                super({ key: 'TestScene' });
            }
            
            create() {
                this.add.text(400, 300, 'Phaser 3 is working!', {
                    fontSize: '32px',
                    fill: '#ff6b35'
                }).setOrigin(0.5);
                
                this.add.rectangle(400, 400, 200, 50, 0xff6b35)
                    .setInteractive()
                    .on('pointerdown', () => {
                        alert('Button clicked!');
                    });
                    
                this.add.text(400, 400, 'Click Me', {
                    fontSize: '20px',
                    fill: '#ffffff'
                }).setOrigin(0.5);
            }
        }
        
        const config = {
            type: Phaser.AUTO,
            width: 800,
            height: 600,
            parent: 'gameContainer',
            backgroundColor: '#1a1a2e',
            scene: TestScene
        };
        
        const game = new Phaser.Game(config);
        console.log('Game initialized');
    </script>
</body>
</html>
