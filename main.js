/**
 * Counter Strike Manager Simulator
 * Phaser 3 Version: 3.70.0
 * A strategic team management game where players build and manage a CS esports team
 */

// Game Configuration
const gameConfig = {
    CANVAS_WIDTH: 1024,
    CANVAS_HEIGHT: 768,
    STARTING_BUDGET: 10000,
    STARTING_TEAM_SIZE: 3,
    MAX_TEAM_SIZE: 5,
    WIN_CONDITION: 10,
    LOSE_BUDGET: -5000,
    COLORS: {
        PRIMARY: 0x1a1a2e,
        SECONDARY: 0xff6b35,
        ACCENT: 0x00d4ff,
        TEXT: 0xe5e5e5,
        WARNING: 0xff4757,
        SUCCESS: 0x2ed573
    }
};

// Game State Management
class GameState {
    constructor() {
        this.budget = gameConfig.STARTING_BUDGET;
        this.team = [];
        this.victories = 0;
        this.round = 1;
        this.difficulty = 1;
        
        // Initialize starting team
        for (let i = 0; i < gameConfig.STARTING_TEAM_SIZE; i++) {
            this.team.push(this.generatePlayer(`Player ${i + 1}`, 30 + Math.random() * 20));
        }
    }
    
    generatePlayer(name, skill = null) {
        return {
            name: name,
            skill: skill || (20 + Math.random() * 30),
            cost: Math.floor((skill || 25) * 100 + Math.random() * 1000)
        };
    }
    
    getTeamSkill() {
        return this.team.reduce((total, player) => total + player.skill, 0) / this.team.length;
    }
    
    canAfford(cost) {
        return this.budget >= cost;
    }
    
    isGameWon() {
        return this.victories >= gameConfig.WIN_CONDITION && this.budget > 0;
    }
    
    isGameLost() {
        return this.budget <= gameConfig.LOSE_BUDGET;
    }
}

// Main Game Scene
class GameScene extends Phaser.Scene {
    constructor() {
        super({ key: 'GameScene' });
        this.gameState = new GameState();
        this.particles = null;
    }
    
    preload() {
        // Create simple colored rectangles as placeholders for sprites
        this.load.image('button', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
        
        // Load audio (using placeholder URLs - in production, replace with actual audio files)
        this.load.audio('bgMusic', ['https://www.soundjay.com/misc/sounds/bell-ringing-05.wav']);
        this.load.audio('clickSound', ['https://www.soundjay.com/misc/sounds/beep-07a.wav']);
        this.load.audio('successSound', ['https://www.soundjay.com/misc/sounds/beep-10.wav']);
        this.load.audio('failSound', ['https://www.soundjay.com/misc/sounds/beep-03.wav']);
    }
    
    create() {
        // Background
        this.add.rectangle(512, 384, 1024, 768, gameConfig.COLORS.PRIMARY);
        
        // Create particle system for effects
        this.particles = this.add.particles(0, 0, 'button', {
            scale: { start: 0.5, end: 0 },
            speed: { min: 50, max: 150 },
            lifespan: 1000,
            tint: gameConfig.COLORS.ACCENT
        });
        
        // UI Setup
        this.createUI();
        this.updateDisplay();
        
        // Audio setup (with error handling)
        try {
            this.bgMusic = this.sound.add('bgMusic', { loop: true, volume: 0.3 });
            this.clickSound = this.sound.add('clickSound', { volume: 0.5 });
            this.successSound = this.sound.add('successSound', { volume: 0.7 });
            this.failSound = this.sound.add('failSound', { volume: 0.7 });
            // this.bgMusic.play(); // Commented out due to browser autoplay policies
        } catch (error) {
            console.log('Audio loading failed - continuing without sound');
        }
    }
    
    createUI() {
        // Title
        this.add.text(512, 50, 'CS MANAGER SIMULATOR', {
            fontSize: '32px',
            fill: '#ff6b35',
            fontFamily: 'Courier New',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // Status Panel
        this.statusText = this.add.text(50, 100, '', {
            fontSize: '16px',
            fill: '#e5e5e5',
            fontFamily: 'Courier New',
            lineSpacing: 5
        });

        // Team Panel
        this.teamText = this.add.text(50, 250, '', {
            fontSize: '14px',
            fill: '#e5e5e5',
            fontFamily: 'Courier New',
            lineSpacing: 3
        });

        // Action Buttons
        this.createButton(600, 150, 'RECRUIT PLAYER', () => this.recruitPlayer());
        this.createButton(600, 200, 'TRAIN TEAM', () => this.trainTeam());
        this.createButton(600, 250, 'COMPETE MATCH', () => this.competeMatch());
        this.createButton(600, 350, 'NEW GAME', () => this.newGame());

        // Available Players Panel
        this.availableText = this.add.text(600, 400, '', {
            fontSize: '12px',
            fill: '#00d4ff',
            fontFamily: 'Courier New',
            lineSpacing: 2
        });

        this.generateAvailablePlayers();
    }

    createButton(x, y, text, callback) {
        const button = this.add.rectangle(x, y, 180, 35, gameConfig.COLORS.SECONDARY)
            .setInteractive()
            .on('pointerdown', () => {
                this.playSound('clickSound');
                this.createButtonEffect(x, y);
                callback();
            })
            .on('pointerover', () => {
                button.setFillStyle(gameConfig.COLORS.ACCENT);
            })
            .on('pointerout', () => {
                button.setFillStyle(gameConfig.COLORS.SECONDARY);
            });

        this.add.text(x, y, text, {
            fontSize: '12px',
            fill: '#ffffff',
            fontFamily: 'Courier New',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        return button;
    }

    createButtonEffect(x, y) {
        this.particles.emitParticleAt(x, y, 10);
    }

    generateAvailablePlayers() {
        this.availablePlayers = [];
        for (let i = 0; i < 3; i++) {
            const skill = 25 + Math.random() * 40 + (this.gameState.round * 2);
            this.availablePlayers.push(
                this.gameState.generatePlayer(`Recruit ${i + 1}`, skill)
            );
        }
    }

    recruitPlayer() {
        if (this.gameState.team.length >= gameConfig.MAX_TEAM_SIZE) {
            this.showMessage('Team is full! (Max 5 players)', gameConfig.COLORS.WARNING);
            return;
        }

        if (this.availablePlayers.length === 0) {
            this.showMessage('No players available!', gameConfig.COLORS.WARNING);
            return;
        }

        const player = this.availablePlayers[0];
        if (!this.gameState.canAfford(player.cost)) {
            this.showMessage('Not enough budget!', gameConfig.COLORS.WARNING);
            this.playSound('failSound');
            return;
        }

        this.gameState.budget -= player.cost;
        this.gameState.team.push(player);
        this.availablePlayers.shift();

        this.playSound('successSound');
        this.showMessage(`Recruited ${player.name}!`, gameConfig.COLORS.SUCCESS);
        this.updateDisplay();
    }

    trainTeam() {
        const cost = 1000 + (this.gameState.round * 200);
        if (!this.gameState.canAfford(cost)) {
            this.showMessage('Not enough budget for training!', gameConfig.COLORS.WARNING);
            this.playSound('failSound');
            return;
        }

        this.gameState.budget -= cost;
        this.gameState.team.forEach(player => {
            player.skill += 3 + Math.random() * 4;
        });

        this.playSound('successSound');
        this.showMessage('Team training completed!', gameConfig.COLORS.SUCCESS);
        this.updateDisplay();
    }

    competeMatch() {
        const teamSkill = this.gameState.getTeamSkill();
        const opponentSkill = 40 + (this.gameState.difficulty * 8) + Math.random() * 20;

        // Match simulation with some randomness
        const skillDifference = teamSkill - opponentSkill;
        const winChance = 0.5 + (skillDifference / 100);
        const matchResult = Math.random() < Math.max(0.1, Math.min(0.9, winChance));

        if (matchResult) {
            // Victory
            const prize = 2000 + (this.gameState.round * 300);
            this.gameState.budget += prize;
            this.gameState.victories++;
            this.gameState.difficulty += 0.2;

            this.playSound('successSound');
            this.showMessage(`VICTORY! +$${prize}`, gameConfig.COLORS.SUCCESS);
            this.createVictoryEffect();
        } else {
            // Defeat
            const penalty = 500 + (this.gameState.round * 100);
            this.gameState.budget -= penalty;

            this.playSound('failSound');
            this.showMessage(`DEFEAT! -$${penalty}`, gameConfig.COLORS.WARNING);
        }

        this.gameState.round++;
        this.generateAvailablePlayers();
        this.updateDisplay();
        this.checkGameEnd();
    }

    createVictoryEffect() {
        // Create celebration particles
        for (let i = 0; i < 5; i++) {
            this.time.delayedCall(i * 100, () => {
                this.particles.emitParticleAt(
                    400 + Math.random() * 200,
                    200 + Math.random() * 100,
                    15
                );
            });
        }
    }

    newGame() {
        this.gameState = new GameState();
        this.generateAvailablePlayers();
        this.updateDisplay();
        this.showMessage('New game started!', gameConfig.COLORS.ACCENT);
    }

    updateDisplay() {
        // Update status
        this.statusText.setText([
            `💰 Budget: $${this.gameState.budget.toLocaleString()}`,
            `🏆 Victories: ${this.gameState.victories}/${gameConfig.WIN_CONDITION}`,
            `📈 Round: ${this.gameState.round}`,
            `⚡ Team Skill: ${this.gameState.getTeamSkill().toFixed(1)}`
        ]);

        // Update team display
        let teamDisplay = '👥 YOUR TEAM:\n';
        this.gameState.team.forEach((player, index) => {
            teamDisplay += `${index + 1}. ${player.name} (Skill: ${player.skill.toFixed(1)})\n`;
        });
        this.teamText.setText(teamDisplay);

        // Update available players
        let availableDisplay = '🔍 AVAILABLE RECRUITS:\n';
        this.availablePlayers.forEach((player, index) => {
            availableDisplay += `${index + 1}. ${player.name}\n`;
            availableDisplay += `   Skill: ${player.skill.toFixed(1)} | Cost: $${player.cost}\n`;
        });
        this.availableText.setText(availableDisplay);
    }

    showMessage(text, color) {
        if (this.messageText) {
            this.messageText.destroy();
        }

        this.messageText = this.add.text(512, 600, text, {
            fontSize: '18px',
            fill: `#${color.toString(16).padStart(6, '0')}`,
            fontFamily: 'Courier New',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // Fade out message after 2 seconds
        this.tweens.add({
            targets: this.messageText,
            alpha: 0,
            duration: 2000,
            onComplete: () => {
                if (this.messageText) {
                    this.messageText.destroy();
                    this.messageText = null;
                }
            }
        });
    }

    playSound(soundKey) {
        try {
            if (this[soundKey]) {
                this[soundKey].play();
            }
        } catch (error) {
            // Silently handle audio errors
        }
    }

    checkGameEnd() {
        if (this.gameState.isGameWon()) {
            this.showMessage('🎉 CONGRATULATIONS! YOU WON! 🎉', gameConfig.COLORS.SUCCESS);
            this.createVictoryEffect();
        } else if (this.gameState.isGameLost()) {
            this.showMessage('💀 GAME OVER - BUDGET DEPLETED! 💀', gameConfig.COLORS.WARNING);
        }
    }
}

// Menu Scene
class MenuScene extends Phaser.Scene {
    constructor() {
        super({ key: 'MenuScene' });
    }

    create() {
        // Background
        this.add.rectangle(512, 384, 1024, 768, gameConfig.COLORS.PRIMARY);

        // Title with glow effect
        const title = this.add.text(512, 200, 'COUNTER STRIKE\nMANAGER SIMULATOR', {
            fontSize: '48px',
            fill: '#ff6b35',
            fontFamily: 'Courier New',
            fontWeight: 'bold',
            align: 'center',
            lineSpacing: 10
        }).setOrigin(0.5);

        // Animated title glow
        this.tweens.add({
            targets: title,
            scaleX: 1.05,
            scaleY: 1.05,
            duration: 2000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });

        // Subtitle
        this.add.text(512, 320, 'Build the ultimate esports team!', {
            fontSize: '24px',
            fill: '#00d4ff',
            fontFamily: 'Courier New'
        }).setOrigin(0.5);

        // Start button
        const startButton = this.add.rectangle(512, 450, 200, 60, gameConfig.COLORS.SECONDARY)
            .setInteractive()
            .on('pointerdown', () => {
                this.scene.start('GameScene');
            })
            .on('pointerover', () => {
                startButton.setFillStyle(gameConfig.COLORS.ACCENT);
            })
            .on('pointerout', () => {
                startButton.setFillStyle(gameConfig.COLORS.SECONDARY);
            });

        this.add.text(512, 450, 'START GAME', {
            fontSize: '20px',
            fill: '#ffffff',
            fontFamily: 'Courier New',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // Instructions
        this.add.text(512, 550, 'Manage your budget • Recruit players • Train your team • Win matches', {
            fontSize: '16px',
            fill: '#e5e5e5',
            fontFamily: 'Courier New',
            alpha: 0.8
        }).setOrigin(0.5);

        // Animated background elements
        this.createBackgroundAnimation();
    }

    createBackgroundAnimation() {
        // Create floating particles for ambiance
        for (let i = 0; i < 20; i++) {
            const particle = this.add.circle(
                Math.random() * 1024,
                Math.random() * 768,
                2 + Math.random() * 3,
                gameConfig.COLORS.ACCENT,
                0.3
            );

            this.tweens.add({
                targets: particle,
                y: particle.y - 100,
                alpha: 0,
                duration: 3000 + Math.random() * 2000,
                delay: Math.random() * 2000,
                repeat: -1,
                onRepeat: () => {
                    particle.y = 768 + 50;
                    particle.x = Math.random() * 1024;
                    particle.alpha = 0.3;
                }
            });
        }
    }
}

// Game Configuration and Initialization
const config = {
    type: Phaser.AUTO,
    width: gameConfig.CANVAS_WIDTH,
    height: gameConfig.CANVAS_HEIGHT,
    parent: 'gameContainer',
    backgroundColor: '#1a1a2e',
    scene: [MenuScene, GameScene],
    physics: {
        default: 'arcade',
        arcade: {
            gravity: { y: 0 },
            debug: false
        }
    },
    scale: {
        mode: Phaser.Scale.FIT,
        autoCenter: Phaser.Scale.CENTER_BOTH,
        min: {
            width: 800,
            height: 600
        },
        max: {
            width: 1600,
            height: 1200
        }
    }
};

// Initialize the game
const game = new Phaser.Game(config);
}
